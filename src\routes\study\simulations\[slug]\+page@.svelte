<script>
	import { Annotate, Bottom, Calculator, Middle, Reference, Review, Submitting, Top, SimBreaking, MinitestIntro } from '$lib/mockTest/simulation';
	
	import { onMount } from 'svelte';
	import { addDoc, collection, serverTimestamp, doc, getDoc } from 'firebase/firestore';
	import { db, user } from '$lib/firebase';
	import { error } from '@sveltejs/kit';
	import { finalAnswers, finalMarked } from '$lib/stores';
	import { get } from 'svelte/store';
	import { browser } from "$app/environment";

	/**
	 * @typedef {Object} CorrectAnswer
	 * @property {Number[]} recommended
	 * @property {Number[]} possible
	 */

	/**
	 * @typedef {Object} Question
	 * @property {String} questionType
	 * @property {?String} topic
	 * @property {?String} intro
	 * @property {String} passage
	 * @property {?String} passage2
	 * @property {String} question
	 * @property {?Number[]} choices
	 * @property {Number | CorrectAnswer} correctAnswer
	 * @property {?String} graph
	 */

	/**
	 * @typedef {Object} Module
	 * @property {String} title
	 * @property {String} simulation
	 * @property {String} module
	 * @property {Question[]} questions
	 * 
	 */

	/**
	 * @typedef {Object} Data
	 * @property {String} title
	 * @property {String} slug
	 * @property {Module[]} modules
	 */

	
	/** @type {{data: Data, innerFunction?: any}} */
	let { data, innerFunction = $bindable(() => null) } = $props();

	let tmpStudentCross;

	let m = $state(0);
	let i = $state(0);

	// Reinitialize student answers and marked status when changing module
	let studentAnswers = $state(Array(data.modules[m].questions.length).fill(null));
	let studentCross = $state([...Array(data.modules[m].questions.length)].map((x) => Array(4).fill(false)));
	let isMarked = $state(new Array(data.modules[m].questions.length).fill(false));

	let submitting = $state(false);

	function setMarked() {
		isMarked[i] = !isMarked[i];
	}

	function setAnswer(index) {
		studentAnswers[i] = index;
	}

	function setCross(index) {
		if (index === studentAnswers[i]) studentAnswers[i] = null;
		tmpStudentCross = studentCross.slice();
		tmpStudentCross[i][index] = !tmpStudentCross[i][index];
		studentCross = tmpStudentCross;
	}

	function nextQuestion() {
		i++;
	}

	function previousQuestion() {
		if (i === 0) return;
		i--;
	}

	function setQuestion(question) {
		i = question;
	}

	function toReview() {
		i = data.modules[m].questions.length;
	}

	function nextModule() {
		submitting = true;

		// TODO: Save the current module's answers
		if (m !== 2) {
			// Reset the submitting status
			setTimeout(() => {
				// Increment the module number
				m += 1;	

				// Save the current module's answers
				finalAnswers.update((current) => [...current, studentAnswers]);
    			finalMarked.update((current) => [...current, isMarked]);

				// Reset the student's answers, marked status and timer
				studentAnswers = Array(data.modules[m].questions.length).fill(null);
				studentCross = [...Array(data.modules[m].questions.length)].map((x) => Array(4).fill(false));
				isMarked = new Array(data.modules[m].questions.length).fill(false);

				now = Date.now();
				end = Date.now() + allotedTime;
				
				// Reset the question number
				i = 0;
				submitting = false;
			}, 1000);
		} else {
			setBreaking();

			// Increment the module number
			m += 1;	
			data.modules[m] = data.modules[m];

			// Reset the student's answers, marked status and timer
			studentAnswers = Array(data.modules[m].questions.length).fill(null);
			studentCross = [...Array(data.modules[m].questions.length)].map((x) => Array(4).fill(false));
			isMarked = new Array(data.modules[m].questions.length).fill(false);

			now = Date.now();
			end = Date.now() + allotedTime;
			
			// Reset the question number
			i = 0;
			submitting = false;
		}
	}


	// FIXME
	async function submit() {
		// Set to true to change screen
		submitting = true;

		// Save the last module's answers
		finalAnswers.update((current) => [...current, [...studentAnswers]]);
    	finalMarked.update((current) => [...current, [...isMarked]]);

		let finalAns = get(finalAnswers);
		let finalMar = get(finalMarked);

		// Scoring
		let scores = [0, 0, 0, 0];
		
		const realModules = data.modules.toSpliced(2, 1);
		for (let i = 0; i < realModules.length; i++) {
			let currentModule = realModules[i];
			for (let j = 0; j < currentModule.questions.length; j++) {
				let currentQuestion = currentModule.questions[j];
				let correctAnswer = currentQuestion.correctAnswer;

				let stuAns = finalAns[i][j];

				if (typeof correctAnswer !== 'object' && correctAnswer === stuAns) {
					scores[i]++;
				} else if (
					typeof correctAnswer === 'object' &&
					stuAns != null &&
					correctAnswer.recommended
						.concat(correctAnswer.possible)
						.some(
							(answer) =>
								stuAns == answer ||
								(stuAns?.includes('/') &&
									stuAns.split('/')[0] / stuAns.split('/')[1] == answer)
						)
				) scores[i]++;
			}
		}
		
		const VERBAL_SCORES = [
			200, 200, 200, 200, 200, 210, 230, 240, 250, 260,  // 0-9
			290, 320, 330, 340, 350, 360, 370, 380, 390, 400,  // 10-19
			410, 430, 440, 440, 450, 460, 480, 480, 490, 500,  // 20-29
			500, 510, 520, 530, 550, 560, 570, 580, 590, 600,  // 30-39
			620, 630, 640, 650, 670, 680, 690, 700, 710, 720,  // 40-49
			730, 750, 770, 790, 800                            // 50-54
		];

		const MATH_SCORES = [
			200, 200, 200, 200, 220, 240, 260, 270, 290, 300,  // 0-9
			320, 330, 350, 360, 380, 390, 410, 420, 440, 450,  // 10-19
			470, 480, 500, 510, 530, 540, 560, 570, 590, 600,  // 20-29
			620, 630, 650, 660, 680, 690, 710, 720, 740, 750,  // 30-39
			760, 770, 780, 790, 800                            // 40-44
		];

		const predictedVerbalScore = VERBAL_SCORES[scores[0] + scores[1]];
		const predictedMathScore = MATH_SCORES[scores[2] + scores[3]];
		
		if (browser) {
			const previousVerbalScore = localStorage.getItem('previousVerbalScore');
			const previousMathScore = localStorage.getItem('previousMathScore');

			if (previousVerbalScore && previousMathScore) {
				localStorage.setItem('previousVerbalScore', predictedVerbalScore);
				localStorage.setItem('previousMathScore', predictedMathScore);
			}
		}

		// TODO: Creating a document to store the result
		const docRef = await addDoc(collection(db, 'Simulation'), {
			user: $user.uid,
			simulation: data.slug,
			answers: finalAns.flat(),
			marked: finalMar.flat(),
			rawModuleScores: scores,
			predictedVerbalScore: predictedVerbalScore,
			predictedMathScore: predictedMathScore,
			predictedTotalScore: predictedVerbalScore + predictedMathScore,
			submitTime: serverTimestamp(),
		});

		// Redirect to walkthrough page
		window.location.href = `/study/analysis/${docRef.id}`;
	}


	
	// Annotate
	let isAnnoTip = $state(false);
	let isAnnotate = $state(false);
	let createNew = false;
	let isCaution = $state(false);
	let annoText = $state();
	let editText = $state();

	let annoIndex = 0;
	let tmpId = $state();

	let annoArray = [];

	function setAnnoTip() {
		isAnnoTip = !isAnnoTip;
	}

	function setAnnotateBut() {
		if (i === data.modules[m].length) return;
		if (!isAnnotate) {
			try {
				let select = document.getSelection();
				let range = select?.getRangeAt(0);
				for (const child of range.cloneContents().children) {
					if (child.tagName === 'SPAN') error(500);
				}
				if (select?.isCollapsed) error(500);
				let span = document.createElement('span');
				range.surroundContents(span);
				span.classList.add('anno-highlight');
				span.id = `anno-highlight-${annoIndex}`;
				span.style.backgroundColor = 'yellow';
				span.onclick = () => {
					handleSelect(span.id);
				};
				range?.collapse();
				annoArray.push('');
				tmpId = annoIndex;
				span.innerHTML += `<span class='anno-desc' id='anno-desc-${annoIndex}'></span>`;
				annoIndex += 1;
				createNew = true;
				setAnnotate();
			} catch {
				setAnnoTip();
			}
		} else handleClose();
	}

	function setAnnotate() {
		if (!isAnnotate) {
			handleEditText();
			isAnnotate = true;
		} else {
			(isAnnotate = false), (createNew = false), (annoText = ''), (isCaution = false);
		}
	}

	function handleEditText() {
		let text = document.getElementById(`anno-highlight-${tmpId}`).firstChild.textContent;
		if (text.length >= 100) {
			let l = 39,
				r = text.length - 40;
			let ar = [' ', '.', ',', ';'];
			while (text[l] !== ar[0] && text[l] !== ar[1] && text[l] !== ar[2] && text[l] !== ar[3]) l++;
			while (text[r] !== ar[0] && text[r] !== ar[1] && text[r] !== ar[2] && text[r] !== ar[3]) r--;
			text = text.slice(0, l) + '...' + text.slice(r + 1);
		}
		editText = text;
	}

	function handleSelect(id) {
		tmpId = parseInt(id.slice(15));
		if (!isAnnotate) setAnnotate();
		annoText = annoArray[tmpId];
	}

	function handleDelete() {
		let tmp = document.getElementById(`anno-highlight-${tmpId}`);
		if (tmp !== null) tmp.removeChild(tmp.lastChild);
		setAnnotate();
		tmp.outerHTML = tmp.innerHTML;
		setContent();
	}

	function handleClose() {
		let tmpInner = document.getElementById(`anno-desc-${tmpId}`);
		if (createNew) {
			handleDelete();
		} else {
			tmpInner.innerText = annoArray[tmpId];
			setAnnotate();
		}
	}



	let setContent = $state();
	let curPassage = $state('');
	let curIntro = $state('');
	let curPassage_2 = '';

	function resetHighlight(element) {
		if (element) return;
		const find = element?.match(/anno-highlight-\d/g);
		find?.forEach((id) => {
			let span = document.getElementById(id);
			span.onclick = () => {
				handleSelect(span.id);
			};
		});
	}

	function resetHighlightAll(arr) {
		arr.forEach((elem) => {
			if (Array.isArray(elem)) {
				elem.forEach((subelem) => {
					resetHighlight(subelem);
				});
			} else {
				resetHighlight(elem);
			}
		});
	}



	// Reference
	let isReferenceOpen = $state(false);

	function openReference() {
		isReferenceOpen = !isReferenceOpen;
	}

	// Calculator
	let isCalculatorOpen = $state(false);

	let isCalculatorCollapsed = true;

	function openCalculator() {
		isCalculatorOpen = !isCalculatorOpen;
	}


	let currentComponent = $state("");
	function setBreaking() {
        currentComponent = isBreaking ? "" : 'breaking';
    }

	// Set up timer
	const allotedTimes = [32, 32, 10, 35, 35];


	let now = $state(Date.now());


    // Make it countdown
    const interval = setInterval(() => { if (!isIntroOpen) now = Date.now() }, 100);


	let isIntroOpen = $state(true);

	function closeIntro() {
		isIntroOpen = false;
		now = Date.now();
    	end = Date.now() + allotedTime;
		innerFunction();
	}

	// Watch for changes to uid
	let isLoading = $state(true);

	if (browser) {
        loadUserData();
		isLoading = false;
    }

    async function loadUserData() {
        const hasAimScore = localStorage.getItem('aimScore');

        if (hasAimScore) {
            isIntroOpen = false;
        } else if ($user) {
            try {
                const docRef = doc(db, 'users', $user.uid);
                const docSnap = await getDoc(docRef);

                if (docSnap.exists()) {
                    const data = docSnap.data();
                    if (data.aimScore) {
                        localStorage.setItem('aimScore', data.aimScore);
                        localStorage.setItem('previousVerbalScore', data.previousVerbalScore);
                        localStorage.setItem('previousMathScore', data.previousMathScore);
                    }
                }
            } catch (error) {
                console.error("Error fetching user data:", error);
            }
        }
    }


	onMount(async () => {
		// * Annotate
		document.addEventListener('keydown', logAnyKey);
		document.addEventListener('mousemove', handleMouseMove);

		function logAnyKey(e) {
			if (isAnnotate && e.key === 'Escape') {
				handleClose();
			}
		}

		function handleMouseMove(e) {
			const x = e.pageX;
			const y = e.pageY;
			const list = document.querySelectorAll('.anno-desc');
			const array = [...list];
			array.forEach((elem) => {
				elem.style.left = x + 'px';
				elem.style.top = y + 'px';
			});
		}

		// Disable copy/cut
		['copy', 'cut'].forEach((event) =>
			document.addEventListener(event, async () => {
				if (!navigator.clipboard) {
					// Create a "hidden" input
					var aux = document.createElement('input');

					// Assign it the value of the specified element
					aux.setAttribute('value', '');

					document.body.appendChild(aux);

					// Highlight its content
					aux.select();

					// Copy the highlighted text
					document.execCommand('copy');

					document.body.removeChild(aux);
				} else {
					// Use clipboard API in case execCommand is not supported
					await navigator.clipboard.writeText('').catch();
				}
			})
		);
	});
	let isMath = $derived(['Algebra', 'Data Analysis', 'Geometry'].includes(data.modules[m]?.questions[i]?.questionType));

	$effect(() => {
		if (!isMath) resetHighlightAll([curPassage, curIntro, curPassage_2]);
	});

	let isSPR = $derived(!data.modules[m].questions[i]?.choices ?? false);
	let isBreaking = $derived(currentComponent === 'breaking');
	let allotedTime = $derived(allotedTimes[m] * 60 * 1000);
    let end = $derived(Date.now() + allotedTime);
	
    // Only takes minutes and seconds
    let count = $derived(Math.floor(end - now));
    let minutes = $derived(Math.floor((count % (1000 * 60 * 60)) / (1000 * 60)));
    let seconds = $derived(Math.floor((count % (1000 * 60)) / 1000).toLocaleString('en-US', { 
        minimumIntegerDigits: 2,
        useGrouping: false
     }));
    // Stop countdown and submit when the time hits 0
    $effect(() => {
		if (count <= 0 && !submitting) {
	        if (m === 4) {
				clearInterval(interval);
				submit();
			} else {
				nextModule();
			} 
	    }
	});
</script>

<svelte:head>
	<link
		rel="stylesheet"
		href="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css"
		integrity="sha384-wcIxkf4k558AjM3Yz3BBFQUbk/zgIYC2R0QpeeYb+TwlBVMrlgLqwRjRtGZiK7ww"
		crossorigin="anonymous"
	/>
	<script
		src="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js"
		integrity="sha384-hIoBPJpTUs74ddyc4bFZSM1TVlQDA60VBbJS0oA934VSz82sBx1X7kSx2ATBDIyd"
		crossorigin="anonymous"
	></script>
	<script
		src="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js"
		integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk"
		crossorigin="anonymous"
	></script>

	<title>{data.title} - DSAT16</title>
</svelte:head>

<svelte:window
	onload={() => {
        if (isMath) renderMathInElement(document.body, {
			delimiters: [
				{ left: '$$', right: '$$', display: true },
				{ left: '$', right: '$', display: false }
			],

			throwOnError: false
		})}}
/>

{#if m == 2}
<SimBreaking setBreaking={nextModule} isInModule={false} {minutes} {seconds} />
{:else if !submitting && !isLoading}
	<div class="hide no-overflow">
		<div class="nonselect">
			<Top
				data={data.modules[m]}
				{isAnnoTip}
				{setAnnoTip}
				{setAnnotateBut}
				{isMath}
				{openReference}
				{openCalculator}
				{i}
				{currentComponent}
				{isBreaking}
				{setBreaking}
				{allotedTime}
				{minutes}
				{seconds}
			/>
		</div>

		{#if isIntroOpen}
			<MinitestIntro uid={$user.uid} bind:saveScores={innerFunction}/>
		{:else if i > data.modules[m].questions.length - 1}
			<Review data={data.modules[m]} {isMarked} {studentAnswers} {setQuestion} />
		<!-- Give the student a 10-minute break after verbal -->
        {:else}
			<!-- {#key isCaution} -->
			<Middle
				data={data.modules[m]}
				{studentAnswers}
				{isMarked}
				{setAnswer}
				{setMarked}
				{i}
				bind:setContent
				bind:curPassage
				bind:curIntro
				{studentCross}
				{setCross}
				{isMath}
				{isSPR}
				{isCalculatorOpen}
			/>
			<!-- {/key} -->
		{/if}

		<div class="nonselect">
			<Bottom
				data={data.modules[m]}
				{isMarked}
				{studentAnswers}
				{i}
				{m}
				{nextModule}
				{nextQuestion}
				{setQuestion}
				{previousQuestion}
				{toReview}
				{submit}
				{finalAnswers}
				{finalMarked}
				{closeIntro}
				{isIntroOpen}
			/>
		</div>

		{#if !isMath}
			<div class={isAnnotate ? 'hide' : 'show hide'}>
				<Annotate
					{setAnnotate}
					{annoArray}
					{tmpId}
					bind:annoText
					{setContent}
					{handleClose}
					{isAnnotate}
					bind:isCaution
					{handleDelete}
					bind:editText
				/>
			</div>
		{/if}

		{#if isReferenceOpen}
			<Reference {openReference} />
		{/if}

		<!-- Don't use if -->
		<div class:show={!isCalculatorOpen}>
			<Calculator {openCalculator} {isCalculatorCollapsed} />
		</div>
	</div>
{:else}
	<Submitting title={m == 4 ? data.title : data.modules[m].title} {isLoading} />
{/if}

<div class="show-wrapper">
	<div class="show">
		<div class="title">{data.title}</div>
		<div class="caution">Lưu ý</div>
		<div class="text">
			This feature is not supported on this device. To access {data.title}, please switch to a device with a larger screen.
		</div>
	</div>
</div>

<style>
	.nonselect {
		user-select: none;
	}

	.show {
		display: none;
	}

	.no-overflow {
		overflow: hidden;
		position: sticky;
		height: 100svh;
	}

	@media only screen and (max-width: 960px) {
		.show-wrapper {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 100vw;
			height: 100vh;
		}

		.show {
			width: 75%;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			gap: 14px;
			margin: 64px;
		}

		.title {
			color: #000;
			font-family: 'Inter';
			font-size: 30px;
			font-style: normal;
			font-weight: 600;
			line-height: normal;
			text-align: center;
		}

		.caution {
			color: #000;
			font-family: 'Inter';
			font-size: 24px;
			font-style: normal;
			font-weight: 600;
			line-height: normal;
		}

		.text {
			color: #000;
			font-family: 'Open Sans';
			font-size: 16px;
			font-style: normal;
			font-weight: 400;
			line-height: 24px;
			text-align: center;
		}

		.hide {
			display: none;
		}
	}
</style>
